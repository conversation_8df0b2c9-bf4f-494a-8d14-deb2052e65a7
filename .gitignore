# Miscellaneous
*.class
*.log
*.pyc
*.swp
.DS_Store
.atom/
.build/
.buildlog/
.history
.svn/
.swiftpm/
migrate_working_dir/

# IntelliJ related
*.iml
*.ipr
*.iws
.idea/

# The .vscode folder contains launch configuration and tasks you configure in
# VS Code which you may wish to be included in version control, so this line
# is commented out by default.
#.vscode/

# Flutter/Dart/Pub related
**/doc/api/
**/ios/Flutter/.last_build_id
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.packages
.pub-cache/
.pub/
/build/
/linux/
/macos/
/windows/


# Symbolication related
app.*.symbols

# Obfuscation related
app.*.map.json

# Android Studio will place build artifacts here
/android/app/debug
/android/app/profile
/android/app/release
.qodo


# Auto-generated native build files
**/build_file_index.txt
**/cmake_install.cmake
**/configure_fingerprint.bin
**/metadata_generation_command.txt
**/symbol_folder_index.txt

# Optionally ignore prefab config
**/prefab_config.json
/android/app/.cxx

# Firebase configuration files (contain sensitive data)
**/google-services.json
**/GoogleService-Info.plist